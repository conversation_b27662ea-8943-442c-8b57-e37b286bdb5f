/**
 * Test script to verify department color functionality
 * This tests the case-insensitive matching and fallback behavior
 */

import { getDepartmentConfigWithFallback, DEFAULT_DEPARTMENT_COLORS } from './features/departments/hooks/useDepartmentColors';
import type { DepartmentColorMapping } from './features/departments/hooks/useDepartmentColors';

// Mock department color mapping based on the database data
const mockDepartmentColorMapping: DepartmentColorMapping = {
  support: {
    label: 'Support Department',
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    dotColor: 'bg-blue-500',
  },
  sales: {
    label: 'Sales Department',
    color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    dotColor: 'bg-green-500',
  },
  technical: {
    label: 'Technical Department',
    color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
    dotColor: 'bg-orange-500',
  },
};

// Test cases
const testCases = [
  // Case-insensitive matching tests
  { input: 'support', expected: mockDepartmentColorMapping.support, description: 'Lowercase support' },
  { input: 'Support', expected: mockDepartmentColorMapping.support, description: 'Capitalized Support' },
  { input: 'SUPPORT', expected: mockDepartmentColorMapping.support, description: 'Uppercase SUPPORT' },
  { input: 'SuPpOrT', expected: mockDepartmentColorMapping.support, description: 'Mixed case SuPpOrT' },
  
  // Other departments
  { input: 'sales', expected: mockDepartmentColorMapping.sales, description: 'Lowercase sales' },
  { input: 'Sales', expected: mockDepartmentColorMapping.sales, description: 'Capitalized Sales' },
  { input: 'technical', expected: mockDepartmentColorMapping.technical, description: 'Lowercase technical' },
  { input: 'Technical', expected: mockDepartmentColorMapping.technical, description: 'Capitalized Technical' },
  
  // Fallback tests
  { input: null, expected: DEFAULT_DEPARTMENT_COLORS, description: 'Null input' },
  { input: undefined, expected: DEFAULT_DEPARTMENT_COLORS, description: 'Undefined input' },
  { input: '', expected: DEFAULT_DEPARTMENT_COLORS, description: 'Empty string' },
  { input: 'nonexistent', expected: DEFAULT_DEPARTMENT_COLORS, description: 'Non-existent department' },
  { input: 'marketing', expected: DEFAULT_DEPARTMENT_COLORS, description: 'Department not in mapping' },
];

// Run tests
console.log('🧪 Testing Department Color Functionality');
console.log('==========================================');

let passedTests = 0;
let totalTests = testCases.length;

testCases.forEach((testCase, index) => {
  const result = getDepartmentConfigWithFallback(testCase.input, mockDepartmentColorMapping);
  const passed = JSON.stringify(result) === JSON.stringify(testCase.expected);
  
  console.log(`Test ${index + 1}: ${testCase.description}`);
  console.log(`  Input: ${testCase.input}`);
  console.log(`  Expected: ${testCase.expected.label} (${testCase.expected.color})`);
  console.log(`  Got: ${result.label} (${result.color})`);
  console.log(`  Result: ${passed ? '✅ PASS' : '❌ FAIL'}`);
  console.log('');
  
  if (passed) passedTests++;
});

console.log(`Summary: ${passedTests}/${totalTests} tests passed`);

if (passedTests === totalTests) {
  console.log('🎉 All tests passed! Department color functionality is working correctly.');
} else {
  console.log('⚠️ Some tests failed. Please check the implementation.');
}

// Export for potential use in other tests
export { mockDepartmentColorMapping, testCases };
