import { useMemo } from 'react';
import { useActiveDepartments, useDepartments } from './useDepartments';
import { useTenant } from '@/features/tenant/store/use-tenant-store';
import { useTenantUuid } from '@/hooks/useRealtimeQuery';

/**
 * Department color configuration interface that matches the static config format
 */
export interface DepartmentColorConfig {
  label: string;
  color: string;
  dotColor: string;
}

/**
 * Department color mapping type that replaces static departmentConfig
 */
export type DepartmentColorMapping = Record<string, DepartmentColorConfig>;

/**
 * Unified hook for department colors that provides real-time color synchronization
 * This replaces the static departmentConfig usage throughout the application
 */
export function useDepartmentColors() {
  const { tenantId } = useTenant();
  const tenantUuidQuery = useTenantUuid(tenantId || '');
  const tenantUuid = tenantUuidQuery.data;

  // Get active departments with real-time updates
  const {
    data: departments,
    isLoading,
    error,
  } = useActiveDepartments(tenantUuid || null);

  // Transform department data into color mapping format
  const departmentColorMapping = useMemo((): DepartmentColorMapping => {
    if (!departments) return {};

    const mapping: DepartmentColorMapping = {};

    departments.forEach((dept) => {
      // Use lowercase department name as key to match ticket.department values
      const key = dept.name.toLowerCase();

      mapping[key] = {
        label: `${dept.name} Department`,
        color: dept.color,
        dotColor: dept.dot_color,
      };
    });

    return mapping;
  }, [departments]);

  // Helper function to get color config for a specific department
  const getDepartmentConfig = (
    departmentName: string
  ): DepartmentColorConfig | null => {
    if (!departmentName) return null;

    // Try exact match first, then lowercase
    const key = departmentName.toLowerCase();
    return departmentColorMapping[key] || null;
  };

  // Helper function to get all available departments for dropdowns
  const getAvailableDepartments = () => {
    return departments || [];
  };

  // Helper function to check if a department exists
  const departmentExists = (departmentName: string): boolean => {
    if (!departmentName) return false;
    const key = departmentName.toLowerCase();
    return key in departmentColorMapping;
  };

  return {
    // Main color mapping that replaces static departmentConfig
    departmentColorMapping,

    // Helper functions
    getDepartmentConfig,
    getAvailableDepartments,
    departmentExists,

    // Raw department data
    departments,

    // Query state
    isLoading,
    error,

    // For backwards compatibility with existing code
    departmentConfig: departmentColorMapping,
  };
}

/**
 * Hook specifically for getting department configuration by name
 * Useful for components that need color info for a specific department
 */
export function useDepartmentConfig(departmentName: string | null | undefined) {
  const { getDepartmentConfig, isLoading } = useDepartmentColors();

  const config = useMemo(() => {
    if (!departmentName) return null;
    return getDepartmentConfig(departmentName);
  }, [departmentName, getDepartmentConfig]);

  return {
    config,
    isLoading,
    exists: config !== null,
  };
}

/**
 * Default fallback colors for when department data is not available
 * This ensures the UI doesn't break if there are any loading issues
 */
export const DEFAULT_DEPARTMENT_COLORS: DepartmentColorConfig = {
  label: 'Department',
  color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
  dotColor: 'bg-gray-500',
};

/**
 * Hook for all departments (including inactive ones)
 * Useful for components that need to display colors for inactive departments
 * FIXED: Use a separate query without admin restrictions for color display
 */
export function useAllDepartmentColors() {
  const { tenantId } = useTenant();
  const tenantUuidQuery = useTenantUuid(tenantId || '');
  const tenantUuid = tenantUuidQuery.data;

  // CRITICAL FIX: Use a separate query for department colors that doesn't require admin permissions
  // This ensures all users can see department colors in tickets
  const departmentColorsQuery = useRealtimeQuery<any>(
    ['department-colors', tenantUuid || ''],
    async () => {
      if (!tenantUuid) {
        throw new Error('Tenant UUID is required');
      }

      console.log('🎨 Fetching department colors for tenant:', tenantUuid);

      // Call the API endpoint for department colors (accessible to all users)
      const response = await fetch('/api/departments');

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Error fetching department colors:', errorData);
        throw new Error(errorData.error || 'Failed to fetch department colors');
      }

      const data = await response.json();
      console.log('✅ Fetched department colors:', data?.length || 0);
      return (data || []);
    },
    'tenant_departments',
    {
      filter: tenantUuid ? `tenant_id=eq.${tenantUuid}` : '',
      queryOptions: {
        enabled: !!tenantUuid, // No admin restriction for color viewing
        staleTime: 1000 * 60 * 10, // 10 minutes - colors don't change frequently
        gcTime: 1000 * 60 * 60, // 1 hour
      },
    }
  );

  const departments = departmentColorsQuery.data;
  const isLoading = departmentColorsQuery.isLoading;
  const error = departmentColorsQuery.error;

  // Transform all department data into color mapping format
  const allDepartmentColorMapping = useMemo((): DepartmentColorMapping => {
    if (!departments) return {};

    const mapping: DepartmentColorMapping = {};

    departments.forEach((dept: any) => {
      // Use lowercase department name as key to match ticket.department values
      const key = dept.name.toLowerCase();

      mapping[key] = {
        label: `${dept.name} Department`,
        color: dept.color,
        dotColor: dept.dot_color,
      };
    });

    return mapping;
  }, [departments]);

  // Helper function to get color config for a specific department (including inactive)
  const getDepartmentConfig = (
    departmentName: string
  ): DepartmentColorConfig | null => {
    if (!departmentName) return null;

    const key = departmentName.toLowerCase();
    return allDepartmentColorMapping[key] || null;
  };

  return {
    departmentColorMapping: allDepartmentColorMapping,
    getDepartmentConfig,
    departments,
    isLoading,
    error,
    departmentConfig: allDepartmentColorMapping,
  };
}

/**
 * Helper function to get department config with fallback
 */
export function getDepartmentConfigWithFallback(
  departmentName: string | null | undefined,
  departmentColorMapping: DepartmentColorMapping
): DepartmentColorConfig {
  if (!departmentName) return DEFAULT_DEPARTMENT_COLORS;

  const key = departmentName.toLowerCase();
  return departmentColorMapping[key] || DEFAULT_DEPARTMENT_COLORS;
}
